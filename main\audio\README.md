# Audio Service Architecture

The audio service is a core component responsible for managing all audio-related functionalities, including capturing audio from the microphone, processing it, encoding/decoding, and playing back audio through the speaker. It is designed to be modular and efficient, running its main operations in dedicated FreeRTOS tasks to ensure real-time performance.
# 音频服务架构 
音频服务是负责管理所有与音频相关功能的核心组件，包括从麦克风捕获音频、对其进行处理、进行编码/解码以及通过扬声器播放音频。该服务设计为模块化且高效，其主要操作在独立的 FreeRTOS 任务中运行，以确保实时性能。
## Key Components

-   **`AudioService`**: The central orchestrator. It initializes and manages all other audio components, tasks, and data queues.
-   **`AudioCodec`**: A hardware abstraction layer (HAL) for the physical audio codec chip. It handles the raw I2S communication for audio input and output.
-   **`AudioProcessor`**: Performs real-time audio processing on the microphone input stream. This typically includes Acoustic Echo Cancellation (AEC), noise suppression, and Voice Activity Detection (VAD). `AfeAudioProcessor` is the default implementation, utilizing the ESP-ADF Audio Front-End.
-   **`WakeWord`**: Detects keywords (e.g., "你好，小智", "Hi, ESP") from the audio stream. It runs independently from the main audio processor until a wake word is detected.
-   **`OpusEncoderWrapper` / `OpusDecoderWrapper`**: Manages the encoding of PCM audio to the Opus format and decoding Opus packets back to PCM. Opus is used for its high compression and low latency, making it ideal for voice streaming.
-   **`OpusResampler`**: A utility to convert audio streams between different sample rates (e.g., resampling from the codec's native sample rate to the required 16kHz for processing).
## 关键组件- *
**`AudioService`**：中央协调器。它初始化并管理所有其他音频组件、任务和数据队列。- **`AudioCodec`**：物理音频编码芯片的硬件抽象层（HAL）。它处理音频输入和输出的原始I2S通信。- **`AudioProcessor`**：对麦克风输入流进行实时音频处理。这通常包括声学回声消除（AEC）、噪声抑制和语音活动检测（VAD）。`AfeAudioProcessor`是默认实现，利用ESP-ADF音频前端。
- **`唤醒词`**：从音频流中检测关键词（例如，“你好，小智”，“Hi, ESP”）。它独立于主音频处理器运行，直到检测到唤醒词。- **`Opus编码器包装器` / `Opus解码器包装器`**：管理将PCM音频编码为Opus格式，以及将Opus数据包解码回PCM。Opus因其高压缩和低延迟而被使用，使其非常适合语音流传输。- **`Opus重采样器`**：一个工具，用于在不同采样率之间转换音频流（例如，从编解码器的原生采样率重采样到处理所需的16kHz）。
## Threading Model

The service operates on three primary tasks to handle the different stages of the audio pipeline concurrently:

1.  **`AudioInputTask`**: Solely responsible for reading raw PCM data from the `AudioCodec`. It then feeds this data to either the `WakeWord` engine or the `AudioProcessor` based on the current state.
2.  **`AudioOutputTask`**: Responsible for playing audio. It retrieves decoded PCM data from the `audio_playback_queue_` and sends it to the `AudioCodec` to be played on the speaker.
3.  **`OpusCodecTask`**: A worker task that handles both encoding and decoding. It fetches raw audio from `audio_encode_queue_`, encodes it into Opus packets, and places them in the `audio_send_queue_`. Concurrently, it fetches Opus packets from `audio_decode_queue_`, decodes them into PCM, and places the result in the `audio_playback_queue_`.
## 线程模型该服务操作于三个主要任务，以并发方式处理音频管道的不同阶段：
1. **`AudioInputTask`**：专门负责从`AudioCodec`读取原始PCM数据。然后根据当前状态将这些数据提供给`WakeWord`引擎或`AudioProcessor`。
2. **`AudioOutputTask`**：负责播放音频。它从`audio_playback_queue_`中检索解码后的PCM数据，并将其发送到`AudioCodec`以通过扬声器播放。
3. **`OpusCodecTask`**：一个工人任务，处理编码和解码。它从`audio_encode_queue_`中获取原始音频，将其编码为Opus数据包，并将其放入`audio_send_queue_`。同时，它从`audio_decode_queue_`中获取Opus数据包，将其解码为PCM，并将结果放入`audio_playback_queue_`。
## Data Flow

There are two primary data flows: audio input (uplink) and audio output (downlink).

### 1. Audio Input (Uplink) Flow

This flow captures audio from the microphone, processes it, encodes it, and prepares it for sending to a server.

```mermaid
graph TD
    subgraph Device
        Mic[("Microphone")] -->|I2S| Codec(AudioCodec)
        
        subgraph AudioInputTask
            Codec -->|Raw PCM| Read(ReadAudioData)
            Read -->|16kHz PCM| Processor(AudioProcessor)
        end

        subgraph OpusCodecTask
            Processor -->|Clean PCM| EncodeQueue(audio_encode_queue_)
            EncodeQueue --> Encoder(OpusEncoder)
            Encoder -->|Opus Packet| SendQueue(audio_send_queue_)
        end

        SendQueue --> |"PopPacketFromSendQueue()"| App(Application Layer)
    end
    
    App -->|Network| Server((Cloud Server))
```

-   The `AudioInputTask` continuously reads raw PCM data from the `AudioCodec`.
-   This data is fed into an `AudioProcessor` for cleaning (AEC, VAD).
-   The processed PCM data is pushed into the `audio_encode_queue_`.
-   The `OpusCodecTask` picks up the PCM data, encodes it into Opus format, and pushes the resulting packet to the `audio_send_queue_`.
-   The application can then retrieve these Opus packets and send them over the network.
- `AudioInputTask` 持续从 `AudioCodec` 中读取原始 PCM 数据。- 这些数据被送入 `AudioProcessor` 进行清理（AEC, VAD）。- 处理后的 PCM 数据被推入 `audio_encode_queue_`。- `OpusCodecTask` 获取 PCM 数据，将其编码为 Opus 格式，并将生成的数据包推送到 `audio_send_queue_`。- 应用程序随后可以检索这些 Opus 数据包并通过网络发送。
### 2. Audio Output (Downlink) Flow

This flow receives encoded audio data, decodes it, and plays it on the speaker.

```mermaid
graph TD
    Server((Cloud Server)) -->|Network| App(Application Layer)

    subgraph Device
        App -->|"PushPacketToDecodeQueue()"| DecodeQueue(audio_decode_queue_)

        subgraph OpusCodecTask
            DecodeQueue -->|Opus Packet| Decoder(OpusDecoder)
            Decoder -->|PCM| PlaybackQueue(audio_playback_queue_)
        end

        subgraph AudioOutputTask
            PlaybackQueue -->|PCM| Codec(AudioCodec)
        end

        Codec -->|I2S| Speaker[("Speaker")]
    end
```

-   The application receives Opus packets from the network and pushes them into the `audio_decode_queue_`.
-   The `OpusCodecTask` retrieves these packets, decodes them back into PCM data, and pushes the data to the `audio_playback_queue_`.
-   The `AudioOutputTask` takes the PCM data from the queue and sends it to the `AudioCodec` for playback.

## Power Management

To conserve energy, the audio codec's input (ADC) and output (DAC) channels are automatically disabled after a period of inactivity (`AUDIO_POWER_TIMEOUT_MS`). A timer (`audio_power_timer_`) periodically checks for activity and manages the power state. The channels are automatically re-enabled when new audio needs to be captured or played. 