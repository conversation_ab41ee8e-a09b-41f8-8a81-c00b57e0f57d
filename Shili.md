## 实现软件AFE/AEC处理回音消除示例的修改部分----版型修改为bread-compact-wifi的示例

# main/Kconfig.projbuild->

        bool "SSD1306, 分辨率128*64"
    config OLED_SH1106_128X64
        bool "SH1106, 分辨率128*64"
    config NO_OLED  //添加部分
        bool "不使用OLED" //添加部分
endchoice

choice DISPLAY_LCD_TYPE

config USE_REALTIME_CHAT
    bool "启用可语音打断的实时对话模式（需要 AEC 支持）"
    default n

    depends on USE_AUDIO_PROCESSOR && (BOARD_TYPE_ESP_BOX_3 || BOARD_TYPE_ESP_BOX || BOARD_TYPE_LICHUANG_DEV || BOARD_TYPE_ESP32S3_KORVO2_V3)  //删除部分
    depends on USE_AUDIO_PROCESSOR && (BOARD_TYPE_ESP_BOX_3 || BOARD_TYPE_ESP_BOX || BOARD_TYPE_LICHUANG_DEV || BOARD_TYPE_ESP32S3_KORVO2_V3 || BOARD_TYPE_BREAD_COMPACT_WIFI) //添加部分
    help
        需要 ESP32 S3 与 AEC 开启，因为性能不够，不建议和微信聊天界面风格同时开启

# main/application.cc->

#if CONFIG_USE_AUDIO_PROCESSOR
    audio_processor_.Initialize(codec, realtime_chat_enabled_);
    audio_processor_.OnOutput([this](std::vector<int16_t>&& data) {
        background_task_->Schedule([this, data = std::move(data)]() mutable {
            opus_encoder_->Encode(std::move(data), [this](std::vector<uint8_t>&& opus) {
                Schedule([this, opus = std::move(opus)]() {
                    protocol_->SendAudio(opus);
                });
            });
        });
    });
    audio_processor_.OnVadStateChange([this](bool speaking) {
        if (device_state_ == kDeviceStateListening) {
            Schedule([this, speaking]() {
                if (speaking) {
                    voice_detected_ = true;
                } else {
                    voice_detected_ = false;
                }
                auto led = Board::GetInstance().GetLed();
                led->OnStateChanged();
            });
        } // 36-47行为删除部分
    });
    // audio_processor_.OnVadStateChange([this](bool speaking) {
    //     if (device_state_ == kDeviceStateListening) {
    //         Schedule([this, speaking]() {
    //             if (speaking) {
    //                 voice_detected_ = true;
    //             } else {
    //                 voice_detected_ = false;
    //             }
    //             auto led = Board::GetInstance().GetLed();
    //             led->OnStateChanged();
    //         });
    //     }
    // });//48-61行为添加部分
#endif


 
# main/audio_codecs/no_audio_codec.cc ->
NoAudioCodecSimplex::NoAudioCodecSimplex(int input_sample_rate, int output_sample_rate, gpio_num_t spk_bclk, gpio_num_t spk_ws, gpio_num_t spk_dout, gpio_num_t mic_sck, gpio_num_t mic_ws, gpio_num_t mic_din) {
    duplex_ = false;
    input_sample_rate_ = input_sample_rate;
    output_sample_rate_ = output_sample_rate;
    //72-77行为添加部分
    input_reference_ = true; // 是否使用参考输入，实现回声消除
    input_channels_ = input_reference_ ? 2 : 1; // 输入通道数

    time_us_write_ = 0;
    time_us_read_ = 0;
    slice_index_ = 0;   

    // Create a new channel for speaker
    i2s_chan_config_t chan_cfg = {
        .id = (i2s_port_t)0,
        .role = I2S_ROLE_MASTER,
        .dma_desc_num = 6,
        .dma_frame_num = 240,
        .auto_clear_after_cb = true,
        .auto_clear_before_cb = false,
        .intr_priority = 0,
    };
    ESP_ERROR_CHECK(i2s_new_channel(&chan_cfg, &tx_handle_, nullptr));
    i2s_std_config_t std_cfg = {
        .clk_cfg = {
            .sample_rate_hz = (uint32_t)output_sample_rate_,
            .clk_src = I2S_CLK_SRC_DEFAULT,
            .mclk_multiple = I2S_MCLK_MULTIPLE_256,
			#ifdef   I2S_HW_VERSION_2
				.ext_clk_freq_hz = 0,
			#endif
        },
        .slot_cfg = {
........
........
........
#if SOC_I2S_SUPPORTS_PDM_RX
    // Create a new channel for MIC in PDM mode
    i2s_chan_config_t rx_chan_cfg = I2S_CHANNEL_DEFAULT_CONFIG((i2s_port_t)0, I2S_ROLE_MASTER);
    ESP_ERROR_CHECK(i2s_new_channel(&rx_chan_cfg, NULL, &rx_handle_));
    i2s_pdm_rx_config_t pdm_rx_cfg = {
        .clk_cfg = I2S_PDM_RX_CLK_DEFAULT_CONFIG((uint32_t)input_sample_rate_),
        /* The data bit-width of PDM mode is fixed to 16 */
        .slot_cfg = I2S_PDM_RX_SLOT_DEFAULT_CONFIG(I2S_DATA_BIT_WIDTH_16BIT, I2S_SLOT_MODE_MONO),
        .gpio_cfg = {
            .clk = mic_sck,
            .din = mic_din,
            .invert_flags = {
                .clk_inv = false,
            },
        },
    };
    ESP_ERROR_CHECK(i2s_channel_init_pdm_rx_mode(rx_handle_, &pdm_rx_cfg));
#else
    ESP_LOGE(TAG, "PDM is not supported");
#endif
    ESP_LOGI(TAG, "Simplex channels created");
}

int NoAudioCodec::Write(const int16_t* data, int samples) {//126行为删除部分
int NoAudioCodec::Write(const int16_t* data, int samples) 
{
    const int32_t play_size = 512;//127-129为添加部分
    std::vector<int32_t> buffer(samples);

    // output_volume_: 0-100
    // volume_factor_: 0-65536
    int32_t volume_factor = pow(double(output_volume_) / 100.0, 2) * 65536;
    for (int i = 0; i < samples; i++) {
        int64_t temp = int64_t(data[i]) * volume_factor; // 使用 int64_t 进行乘法运算
        if (temp > INT32_MAX) {
            buffer[i] = INT32_MAX;
        } else if (temp < INT32_MIN) {
            buffer[i] = INT32_MIN;
        } else {
            buffer[i] = static_cast<int32_t>(temp); //138-142为删除部分
    {
        std::unique_lock<std::mutex> lock(mutex_);
        if (output_buffer_.size() < play_size*10) {
            output_buffer_.resize(play_size*10,0);
            slice_index_ = 0;
        }

        for (int i = 0; i < samples; i++) {
            int64_t temp = int64_t(data[i]) * volume_factor; // 使用 int64_t 进行乘法运算
            if (temp > INT32_MAX) {
                buffer[i] = INT32_MAX;
            } else if (temp < INT32_MIN) {
                buffer[i] = INT32_MIN;
            } else {
                buffer[i] = static_cast<int32_t>(temp);
            }
            output_buffer_[slice_index_] = data[i];
            slice_index_++;
            if(slice_index_ >= play_size*10) slice_index_ = 0;//143-161为添加部分
        }
    }//163为删除部分

        time_us_write_ = esp_timer_get_time(); // 获取微秒级时间戳
        // ESP_LOGE("NoAudioCodec", "slice_index1 = %ld, %llu", slice_index_,time_us_write_);
    }//165-167为添加部分
    size_t bytes_written;
    ESP_ERROR_CHECK(i2s_channel_write(tx_handle_, buffer.data(), samples * sizeof(int32_t), &bytes_written, portMAX_DELAY));

    return bytes_written / sizeof(int32_t);
}

int NoAudioCodec::Read(int16_t* dest, int samples) {
    size_t bytes_read;

    std::vector<int32_t> bit32_buffer(samples);
    if (i2s_channel_read(rx_handle_, bit32_buffer.data(), samples * sizeof(int32_t), &bytes_read, portMAX_DELAY) != ESP_OK) {
        ESP_LOGE(TAG, "Read Failed!");
        return 0;
    }

    samples = bytes_read / sizeof(int32_t);
    for (int i = 0; i < samples; i++) {
        int32_t value = bit32_buffer[i] >> 12;
        dest[i] = (value > INT16_MAX) ? INT16_MAX : (value < -INT16_MAX) ? -INT16_MAX : (int16_t)value;
    }
    return samples; //175-188为删除部分
    //static int32_t delay_index = 1536;
    static int32_t i_index = 0;
    static bool first_speak = true;
    const int32_t play_size = 512;

   {
       std::unique_lock<std::mutex> lock(mutex_);
       time_us_read_  = esp_timer_get_time(); // 获取微秒级时间戳
       if(time_us_read_ - time_us_write_ > 1000*100 ) // 100ms
       {
           std::fill(output_buffer_.begin(), output_buffer_.end(), 0);
           first_speak = true;
           slice_index_ = 0;
           i_index = play_size*10 - 512;
       }
       else
       {
           if(first_speak)
           {
               first_speak = false;
               i_index = 0;
           }
       }
       if(i_index < 0) i_index = play_size*10 + i_index;
    //    ESP_LOGE("NoAudioCodec", "slice_index2 = %ld, %llu, %ld", slice_index_,time_us_read_, i_index);

   }

   size_t bytes_read;
   std::vector<int32_t> bit32_buffer(samples/2);
   if (i2s_channel_read(rx_handle_, bit32_buffer.data(), samples/2 * sizeof(int32_t), &bytes_read, portMAX_DELAY) != ESP_OK) {
       ESP_LOGE(TAG, "Read Failed!");
       return 0;
   }

   samples = bytes_read / sizeof(int32_t);
   for (int i = 0; i < samples; i++) {
#if CONFIG_USE_REALTIME_CHAT
       int32_t value = bit32_buffer[i] >> 8;
       int64_t temp = int64_t(value) / 256; // 使用 int64_t 进行乘法运算
       dest[i*2] = (temp > INT16_MAX) ? INT16_MAX : (temp < -INT16_MAX) ? -INT16_MAX : (int16_t)temp;
#else
       int32_t value = bit32_buffer[i] >> 12;
       dest[i*2] = (value > INT16_MAX) ? INT16_MAX : (value < -INT16_MAX) ? -INT16_MAX : (int16_t)value;
#endif
       if(output_buffer_.size()> i_index )
       {
           dest[i*2 + 1] = output_buffer_[i_index];
       }
       else
       {
           dest[i*2 + 1] = 0;
       }

    //    dest[i*2 + 1] = 0;
       i_index ++; 
       if(i_index >= play_size*10) i_index = i_index - play_size*10;
   }
   return samples*2; //189-247为添加部分
}

int NoAudioCodecSimplexPdm::Read(int16_t* dest, int samples) {
    size_t bytes_read;
    // PDM 解调后的数据位宽为 16 位
    std::vector<int16_t> bit16_buffer(samples);
    if (i2s_channel_read(rx_handle_, bit16_buffer.data(), samples * sizeof(int16_t), &bytes_read, portMAX_DELAY) != ESP_OK) {
        ESP_LOGE(TAG, "Read Failed!");
        return 0;
    }
    // 计算实际读取的样本数
    samples = bytes_read / sizeof(int16_t);
    // 将 16 位数据直接复制到目标缓冲区
    memcpy(dest, bit16_buffer.data(), samples * sizeof(int16_t));
    return samples;
}

 
# main/audio_codecs/no_audio_codec.h->

#include <driver/gpio.h>
#include <driver/i2s_pdm.h>

#include <mutex>//添加部分
#include <vector>//添加部分

class NoAudioCodec : public AudioCodec {
private:
    virtual int Write(const int16_t* data, int samples) override;
    virtual int Read(int16_t* dest, int samples) override;

public:
    std::vector<int16_t> output_buffer_;
    std::mutex mutex_;
    int32_t slice_index_ = 0;
    uint64_t time_us_write_ = 0;
    uint64_t time_us_read_ = 0; //279-284为添加部分
public:
    virtual ~NoAudioCodec();
};
class NoAudioCodecDuplex : public NoAudioCodec {
public:
    NoAudioCodecDuplex(int input_sample_rate, int output_sample_rate, gpio_num_t bclk, gpio_num_t ws, gpio_num_t dout, gpio_num_t din);
};


# main/audio_processing/audio_processor.cc->

void AudioProcessor::Initialize(AudioCodec* codec, bool realtime_chat) {
    codec_ = codec;
    int ref_num = codec_->input_reference() ? 1 : 0;
    std::string input_format;
    for (int i = 0; i < codec_->input_channels() - ref_num; i++) {
        input_format.push_back('M');
    }
    for (int i = 0; i < ref_num; i++) {
        input_format.push_back('R');
    }
    srmodel_list_t *models = esp_srmodel_init("model");
    char* ns_model_name = esp_srmodel_filter(models, ESP_NSNET_PREFIX, NULL);
    afe_config_t* afe_config = afe_config_init(input_format.c_str(), NULL, AFE_TYPE_VC, AFE_MODE_HIGH_PERF);
    if (realtime_chat) {
        afe_config->aec_init = true;
        afe_config->aec_mode = AEC_MODE_VOIP_LOW_COST; //删除部分
        afe_config->aec_mode = AEC_MODE_VOIP_HIGH_PERF; //添加部分
    } else {
        afe_config->aec_init = false;
    }
    afe_config->ns_init = true;
    afe_config->ns_model_name = ns_model_name;
    afe_config->afe_ns_mode = AFE_NS_MODE_NET;
    if (realtime_chat) {//删除部分
    // if (realtime_chat) {//添加部分
        afe_config->vad_init = false;
    } else {
        afe_config->vad_init = true;
        afe_config->vad_mode = VAD_MODE_0;
        afe_config->vad_min_noise_ms = 100;
    }//322-326为删除部分
    // } 
    // else {
    //     afe_config->vad_init = true;
    //     afe_config->vad_mode = VAD_MODE_0;
    //     afe_config->vad_min_noise_ms = 100;
    // } //327-332为添加部分
    afe_config->afe_perferred_core = 1;
    afe_config->afe_perferred_priority = 1;
    afe_config->agc_init = false;
    afe_config->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM;
    afe_iface_ = esp_afe_handle_from_config(afe_config);
    afe_data_ = afe_iface_->create_from_config(afe_config);
    
    xTaskCreate([](void* arg) {
        auto this_ = (AudioProcessor*)arg;
        this_->AudioProcessorTask();
        vTaskDelete(NULL);
    }, "audio_communication", 4096, this, 3, NULL);
}
........
........
........
void AudioProcessor::AudioProcessorTask() {
    auto fetch_size = afe_iface_->get_fetch_chunksize(afe_data_);
    auto feed_size = afe_iface_->get_feed_chunksize(afe_data_);
    ESP_LOGI(TAG, "Audio communication task started, feed size: %d fetch size: %d",
        feed_size, fetch_size);
    while (true) {
        xEventGroupWaitBits(event_group_, PROCESSOR_RUNNING, pdFALSE, pdTRUE, portMAX_DELAY);
        auto res = afe_iface_->fetch_with_delay(afe_data_, portMAX_DELAY);
        if ((xEventGroupGetBits(event_group_) & PROCESSOR_RUNNING) == 0) {
            continue;
        }
        if (res == nullptr || res->ret_value == ESP_FAIL) {
            if (res != nullptr) {
                ESP_LOGI(TAG, "Error code: %d", res->ret_value);
            }
            continue;
        }

        // VAD state change
        if (vad_state_change_callback_) {
            if (res->vad_state == VAD_SPEECH && !is_speaking_) {
                is_speaking_ = true;
                vad_state_change_callback_(true);
            } else if (res->vad_state == VAD_SILENCE && is_speaking_) {
                is_speaking_ = false;
                vad_state_change_callback_(false);
            }
        }//367-376为删除部分

        // // VAD state change
        // if (vad_state_change_callback_) {
        //     if (res->vad_state == VAD_SPEECH && !is_speaking_) {
        //         is_speaking_ = true;
        //         vad_state_change_callback_(true);
        //     } else if (res->vad_state == VAD_SILENCE && is_speaking_) {
        //         is_speaking_ = false;
        //         vad_state_change_callback_(false);
        //     }
        // }

        // ESP_LOGI("AudioProcessor", "fetch res size: %d", res->data_size/ sizeof(int16_t));//378-389为添加部分

        if (output_callback_) {
            output_callback_(std::vector<int16_t>(res->data, res->data + res->data_size / sizeof(int16_t)));
        }


# main/audio_processing/wake_word_detect.cc->

void WakeWordDetect::Initialize(AudioCodec* codec) {
    codec_ = codec;
    int ref_num = codec_->input_reference() ? 1 : 0;
    srmodel_list_t *models = esp_srmodel_init("model");
    for (int i = 0; i < models->num; i++) {
        ESP_LOGI(TAG, "Model %d: %s", i, models->model_name[i]);
        if (strstr(models->model_name[i], ESP_WN_PREFIX) != NULL) {
            wakenet_model_ = models->model_name[i];
            auto words = esp_srmodel_get_wake_words(models, wakenet_model_);
            // split by ";" to get all wake words
            std::stringstream ss(words);
            std::string word;
            while (std::getline(ss, word, ';')) {
                wake_words_.push_back(word);
            }
        }
    }
    std::string input_format;
    for (int i = 0; i < codec_->input_channels() - ref_num; i++) {
        input_format.push_back('M');
    }
    for (int i = 0; i < ref_num; i++) {
        input_format.push_back('R');
    }
    afe_config_t* afe_config = afe_config_init(input_format.c_str(), models, AFE_TYPE_SR, AFE_MODE_HIGH_PERF);
    afe_config->aec_init = codec_->input_reference();//删除部分
    afe_config->aec_mode = AEC_MODE_SR_HIGH_PERF;//删除部分
    afe_config->aec_init = codec_->input_reference();// 添加部分
    afe_config->aec_mode = AEC_MODE_VOIP_HIGH_PERF;//添加部分

    afe_config->afe_perferred_core = 1;
    afe_config->afe_perferred_priority = 1;
    afe_config->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM;
    
    afe_iface_ = esp_afe_handle_from_config(afe_config);
    afe_data_ = afe_iface_->create_from_config(afe_config);
    xTaskCreate([](void* arg) {
        auto this_ = (WakeWordDetect*)arg;
        this_->AudioDetectionTask();
        vTaskDelete(NULL);
    }, "audio_detection", 4096, this, 3, nullptr);
}
# main/boards/bread-compact-wifi/compact_wifi_board.cc->

public:
    CompactWifiBoard() :
        boot_button_(BOOT_BUTTON_GPIO),
        touch_button_(TOUCH_BUTTON_GPIO),
        volume_up_button_(VOLUME_UP_BUTTON_GPIO),
        volume_down_button_(VOLUME_DOWN_BUTTON_GPIO) {
        InitializeDisplayI2c();//删除部分
        InitializeSsd1306Display();//删除部分

        if(DISPLAY_HEIGHT != 0)
        {
            InitializeDisplayI2c();
            InitializeSsd1306Display();
        }    
        else
        {
            display_ = new NoDisplay();
        } //451-459为添加部分
        InitializeButtons();
        InitializeIot();
    }
    virtual Led* GetLed() override {
        static SingleLed led(BUILTIN_LED_GPIO);
        return &led;
    }
 
# main/boards/bread-compact-wifi/config.h->

#define AUDIO_INPUT_SAMPLE_RATE  16000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000//删除部分
#define AUDIO_OUTPUT_SAMPLE_RATE 16000//添加部分

// 如果使用 Duplex I2S 模式，请注释下面一行
#define AUDIO_I2S_METHOD_SIMPLEX

#elif CONFIG_OLED_SH1106_128X64
#define DISPLAY_HEIGHT  64
#define SH1106
#elif CONFIG_NO_OLED//添加部分
#define DISPLAY_HEIGHT  0//添加部分
