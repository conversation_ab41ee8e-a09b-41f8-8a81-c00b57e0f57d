# Otto Robot AFE/AEC 扩展测试指南

## 修改总结

已成功将Shili.md中的AFE/AEC处理方式套用到otto-robot板子上，主要修改包括：

### 1. Kconfig配置修改
- 在`main/Kconfig.projbuild`中将`BOARD_TYPE_OTTO_ROBOT`添加到支持设备端AEC的板子列表

### 2. NoAudioCodec头文件修改 (`main/audio/codecs/no_audio_codec.h`)
- 添加了`#include <mutex>`和`#include <vector>`
- 在NoAudioCodec类中添加了AEC相关成员变量：
  ```cpp
  std::vector<int16_t> output_buffer_;  // 扬声器输出缓冲区
  std::mutex mutex_;                    // 线程安全保护
  int32_t slice_index_ = 0;            // 缓冲区索引
  uint64_t time_us_write_ = 0;         // 写入时间戳
  uint64_t time_us_read_ = 0;          // 读取时间戳
  ```

### 3. NoAudioCodec实现修改 (`main/audio/codecs/no_audio_codec.cc`)
- 添加了`#include <esp_timer.h>`
- 在`NoAudioCodecSimplex`构造函数中启用参考输入：
  ```cpp
  input_reference_ = true;
  input_channels_ = 2;
  ```
- 修改`Write`方法：存储扬声器输出作为参考信号
- 修改`Read`方法：提供双通道数据（麦克风+参考信号）

## 测试步骤

### 1. 编译配置
```bash
idf.py set-target esp32s3
idf.py menuconfig
```

在menuconfig中：
- 选择板子类型：`Xiaozhi Assistant -> Board Type -> ottoRobot`
- 启用音频处理：`Xiaozhi Assistant -> Enable Audio Noise Reduction -> Y`
- 启用设备端AEC：`Xiaozhi Assistant -> Enable Device-Side AEC -> Y`

### 2. 编译和烧录
```bash
idf.py build flash monitor
```

### 3. 功能验证
1. **基本音频功能**：确认麦克风录音和扬声器播放正常
2. **AEC功能**：在播放音频时说话，检查是否有回声消除效果
3. **日志检查**：查看是否有AEC相关的初始化日志

## 预期效果

1. **AFE初始化**：应该看到AFE以"MR"格式初始化（M=麦克风，R=参考信号）
2. **AEC启用**：在启用设备端AEC时，应该看到相关日志
3. **回声消除**：在播放音频时说话，回声应该被显著减少

## 注意事项

1. **性能影响**：AEC处理会增加CPU和内存使用，注意监控系统性能
2. **时间同步**：参考信号的时间同步很重要，可能需要根据实际硬件调整延迟参数
3. **音质影响**：初次启用可能需要调整参数以获得最佳音质

## 故障排除

1. **编译错误**：检查所有头文件是否正确包含
2. **AEC不工作**：确认配置正确启用，检查input_reference_是否为true
3. **音质问题**：可能需要调整缓冲区大小或延迟参数

## 进一步优化

1. **参数调优**：根据otto-robot的具体硬件特性调整缓冲区大小和延迟
2. **性能优化**：监控CPU使用率，必要时优化算法
3. **音质调优**：根据实际效果调整AEC参数
